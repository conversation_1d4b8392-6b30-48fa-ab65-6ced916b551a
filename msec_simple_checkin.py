#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MSEC简化自动签到脚本
功能：最简单的MSEC平台自动签到，只保留核心网络请求
"""

import requests
import json
import os
import stat
import base64
import time
import threading
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple, Any, List
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


class MSECConfig:
    """MSEC平台配置管理类"""

    # 基础配置
    BASE_URL = "https://msec.nsfocus.com"
    TIMEOUT = 30

    # 文件配置
    DEFAULT_TOKEN_FILE = "msec_tokens.json"
    FILE_PERMISSIONS = 0o600  # 仅所有者可读写

    # 加密配置
    SALT_LENGTH = 16
    PBKDF2_ITERATIONS = 100000
    KEY_LENGTH = 32

    # 验证码配置
    YUNMA_API_URL = "http://api.jfbym.com/api/YmServer/customApi"
    CAPTCHA_TYPE = "50103"
    CAPTCHA_TIMEOUT = 10

    # 重试配置
    MAX_LOGIN_ATTEMPTS = 3
    MAX_CAPTCHA_REFRESH = 2
    USER_INTERVAL_SECONDS = 3

    # 时间配置
    DEFAULT_TOKEN_EXPIRE_HOURS = 24

    # 请求头配置
    DEFAULT_HEADERS = {
        'Host': 'msec.nsfocus.com',
        'Content-Type': 'application/json',
        'Accept': '*/*',
        'Origin': 'https://msec.nsfocus.com',
        'Referer': 'https://msec.nsfocus.com/auth/login',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }


class APIEndpoints:
    """API端点常量"""

    # 账户相关
    CAPTCHA = '/backend_api/account/captcha'
    LOGIN = '/backend_api/account/login'
    USER_INFO = '/backend_api/account/info'

    # 积分相关
    POINTS = '/backend_api/point/common/get'

    # 角色相关
    ROLES = '/backend_api/rbac/role/self/list'

    # 签到相关
    CHECKIN = '/backend_api/checkin/checkin'
    CHECKIN_HISTORY = '/backend_api/checkin/history'


class ResponseStatus:
    """响应状态码常量"""

    SUCCESS = 200
    ALREADY_CHECKED = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    SERVER_ERROR = 500


class CaptchaErrorCodes:
    """验证码识别错误码"""

    SUCCESS = 10000
    PARAM_ERROR = 10001
    INSUFFICIENT_BALANCE = 10002
    NO_ACCESS_PERMISSION = 10003
    INVALID_CAPTCHA_TYPE = 10004
    NETWORK_CONGESTION = 10005
    DATA_OVERLOAD = 10006
    SERVICE_BUSY = 10007
    NETWORK_ERROR = 10008
    RESULT_PREPARING = 10009
    REQUEST_ENDED = 10010

    ERROR_MESSAGES = {
        SUCCESS: "识别成功",
        PARAM_ERROR: "参数错误",
        INSUFFICIENT_BALANCE: "余额不足",
        NO_ACCESS_PERMISSION: "无此访问权限",
        INVALID_CAPTCHA_TYPE: "无此验证类型",
        NETWORK_CONGESTION: "网络拥塞",
        DATA_OVERLOAD: "数据包过载",
        SERVICE_BUSY: "服务繁忙",
        NETWORK_ERROR: "网络错误，请稍后重试",
        RESULT_PREPARING: "结果准备中，请稍后再试",
        REQUEST_ENDED: "请求结束"
    }


class MSECException(Exception):
    """MSEC平台异常基类"""

    def __init__(self, message: str, error_code: Optional[int] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code


class LoginFailedException(MSECException):
    """登录失败异常"""
    pass


class CaptchaException(MSECException):
    """验证码相关异常"""
    pass


class TokenException(MSECException):
    """Token相关异常"""
    pass


class APIException(MSECException):
    """API请求异常"""
    pass


class CheckinException(MSECException):
    """签到异常"""
    pass


class ConfigurationException(MSECException):
    """配置异常"""
    pass


class SecurityManager:
    """文件安全管理器"""

    @staticmethod
    def check_and_set_file_permissions(file_path: str) -> None:
        """检查并设置文件安全权限"""
        if os.path.exists(file_path):
            try:
                file_stat = os.stat(file_path)
                current_mode = file_stat.st_mode & 0o777

                # 检查文件权限是否安全 (应该只有所有者可读写)
                if current_mode != MSECConfig.FILE_PERMISSIONS:
                    print(f"⚠️ 警告: Token文件权限不安全 ({oct(current_mode)})，正在修复...")
                    os.chmod(file_path, MSECConfig.FILE_PERMISSIONS)
                    print("✅ 文件权限已设置为安全模式 (600)")
            except Exception as e:
                print(f"⚠️ 无法检查文件权限: {e}")


class CryptoManager:
    """加密管理器"""

    @staticmethod
    def generate_key(password: str, salt: Optional[bytes] = None) -> tuple[bytes, bytes]:
        """
        基于密码生成加密密钥

        Args:
            password: 用户密码
            salt: 盐值，如果为None则生成新的

        Returns:
            (key, salt) 元组
        """
        if salt is None:
            salt = os.urandom(MSECConfig.SALT_LENGTH)  # 生成随机盐值

        # 使用PBKDF2派生密钥
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=MSECConfig.KEY_LENGTH,
            salt=salt,
            iterations=MSECConfig.PBKDF2_ITERATIONS,  # 迭代次数，增加破解难度
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key, salt

    @staticmethod
    def encrypt_data(data: Dict[str, Any], password: str) -> str:
        """加密数据"""
        key, salt = CryptoManager.generate_key(password)
        fernet = Fernet(key)

        json_data = json.dumps(data, ensure_ascii=False)
        encrypted_data = fernet.encrypt(json_data.encode('utf-8'))

        # 返回 base64(salt + encrypted_data)
        combined = salt + encrypted_data
        return base64.b64encode(combined).decode('utf-8')

    @staticmethod
    def decrypt_data(encrypted_data: str, password: str) -> Optional[Dict[str, Any]]:
        """解密数据"""
        try:
            combined = base64.b64decode(encrypted_data.encode('utf-8'))
            salt = combined[:MSECConfig.SALT_LENGTH]
            encrypted_content = combined[MSECConfig.SALT_LENGTH:]

            key, _ = CryptoManager.generate_key(password, salt)
            fernet = Fernet(key)

            decrypted_data = fernet.decrypt(encrypted_content)
            return json.loads(decrypted_data.decode('utf-8'))
        except Exception:
            return None


class TokenStorage(ABC):
    """Token存储接口"""

    @abstractmethod
    def save_token(self, username: str, token: str, password: str, expire_time: Optional[int] = None) -> bool:
        """保存Token"""
        ...

    @abstractmethod
    def load_token(self, username: str, password: str) -> Optional[str]:
        """加载Token"""
        ...

    @abstractmethod
    def is_token_valid(self, username: str, password: str) -> bool:
        """检查Token是否有效"""
        ...

    @abstractmethod
    def clear_token(self, username: str) -> bool:
        """清除Token"""
        ...


class EncryptedFileStorage(TokenStorage):
    """加密文件存储实现"""

    def __init__(self, storage_file: str = MSECConfig.DEFAULT_TOKEN_FILE):
        """
        初始化加密文件存储

        Args:
            storage_file: 存储文件路径
        """
        self.storage_file = storage_file
        self.file_lock = threading.Lock()  # 文件锁，保证并发安全
        SecurityManager.check_and_set_file_permissions(self.storage_file)


class UnifiedTokenStorage(EncryptedFileStorage):
    """统一Token存储类 - 所有用户Token存储在同一文件中"""

    def __init__(self, storage_file: str = MSECConfig.DEFAULT_TOKEN_FILE):
        """
        初始化统一Token存储

        Args:
            storage_file: 存储文件路径
        """
        super().__init__(storage_file)

    def _load_file(self) -> Dict[str, Any]:
        """加载存储文件"""
        try:
            if os.path.exists(self.storage_file):
                with open(self.storage_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception:
            pass

        # 返回默认结构
        return {
            "users": {},
            "metadata": {
                "version": "1.0",
                "total_users": 0,
                "last_updated": int(time.time())
            }
        }

    def _save_file(self, data: Dict[str, Any]) -> bool:
        """保存数据到文件"""
        try:
            with open(self.storage_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            # 设置安全的文件权限
            SecurityManager.check_and_set_file_permissions(self.storage_file)
            return True
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
            return False

    def _encrypt_user_data(self, user_data: Dict[str, Any], password: str) -> str:
        """加密用户数据"""
        return CryptoManager.encrypt_data(user_data, password)

    def _decrypt_user_data(self, encrypted_data: str, password: str) -> Optional[Dict[str, Any]]:
        """解密用户数据"""
        return CryptoManager.decrypt_data(encrypted_data, password)

    def _is_expired(self, user_data: Dict[str, Any]) -> bool:
        """检查Token是否过期"""
        expire_time = user_data.get("expire_time", 0)
        current_time = int(time.time())
        return current_time >= expire_time

    def _remove_user(self, username: str) -> bool:
        """从文件中移除用户"""
        try:
            data = self._load_file()
            if username in data["users"]:
                del data["users"][username]
                data["metadata"]["total_users"] = len(data["users"])
                data["metadata"]["last_updated"] = int(time.time())
                return self._save_file(data)
        except Exception:
            pass
        return False

    def save_token(self, username: str, token: str, password: str,
                   expire_time: Optional[int] = None) -> bool:
        """
        保存Token到统一文件

        Args:
            username: 用户名
            token: JWT Token
            password: 用户密码（用于加密）
            expire_time: Token过期时间戳

        Returns:
            保存是否成功
        """
        with self.file_lock:
            try:
                # 1. 读取现有数据
                data = self._load_file()

                # 2. 准备用户数据
                user_data: Dict[str, Any] = {
                    "token": token,
                    "expire_time": expire_time or (int(time.time()) + MSECConfig.DEFAULT_TOKEN_EXPIRE_HOURS * 3600)
                }

                # 3. 加密用户数据
                encrypted_data = self._encrypt_user_data(user_data, password)

                # 4. 更新文件数据
                data["users"][username] = encrypted_data
                data["metadata"]["total_users"] = len(data["users"])
                data["metadata"]["last_updated"] = int(time.time())

                # 5. 保存文件
                if self._save_file(data):
                    print(f"✅ Token已保存到 {self.storage_file}")
                    return True
                else:
                    return False

            except Exception as e:
                print(f"❌ 保存Token失败: {e}")
                return False

    def load_token(self, username: str, password: str) -> Optional[str]:
        """
        从统一文件加载Token

        Args:
            username: 用户名
            password: 用户密码（用于解密）

        Returns:
            Token字符串，失败返回None
        """
        with self.file_lock:
            try:
                # 1. 读取文件数据
                data = self._load_file()

                # 2. 检查用户是否存在
                if username not in data["users"]:
                    return None

                # 3. 解密用户数据
                user_data = self._decrypt_user_data(data["users"][username], password)
                if not user_data:
                    print("❌ 解密失败，密码可能错误")
                    return None

                # 4. 检查Token是否过期
                if self._is_expired(user_data):
                    print("⚠️ Token已过期")
                    # 自动清理过期Token
                    self._remove_user(username)
                    return None

                print("✅ Token加载成功")
                return user_data.get("token")

            except Exception as e:
                print(f"❌ 加载Token失败: {e}")
                return None

    def is_token_valid(self, username: str, password: str) -> bool:
        """
        检查Token是否有效（存在且未过期）

        Args:
            username: 用户名
            password: 用户密码

        Returns:
            Token是否有效
        """
        with self.file_lock:
            try:
                data = self._load_file()
                if username not in data["users"]:
                    return False

                user_data = self._decrypt_user_data(data["users"][username], password)
                if not user_data:
                    return False

                return not self._is_expired(user_data)
            except Exception:
                return False

    def clear_token(self, username: str) -> bool:
        """
        清除指定用户的Token

        Args:
            username: 用户名

        Returns:
            清除是否成功
        """
        return self.secure_clear_token(username)

    def secure_clear_token(self, username: str) -> bool:
        """
        安全地清除指定用户的Token

        Args:
            username: 用户名

        Returns:
            清除是否成功
        """
        with self.file_lock:
            try:
                data = self._load_file()
                if username in data["users"]:
                    # 安全地覆盖内存中的数据
                    data["users"][username] = "0" * len(data["users"][username])
                    del data["users"][username]
                    data["metadata"]["total_users"] = len(data["users"])
                    data["metadata"]["last_updated"] = int(time.time())
                    return self._save_file(data)
                return True
            except Exception as e:
                print(f"❌ 清除Token失败: {e}")
                return False


def get_week_checkin_params() -> Tuple[str, int]:
    """获取本周签到历史查询参数"""
    today = datetime.now()
    weekday = today.weekday()
    monday = today - timedelta(days=weekday)
    start_date = monday.strftime('%Y-%m-%d')
    days = weekday + 1
    return start_date, days


def parse_env_users() -> List[Tuple[str, str]]:
    """
    从环境变量解析多用户配置

    Returns:
        用户名密码对列表 [(username, password), ...]
    """
    users_env = os.getenv('MSEC_USER', '').strip()
    passwords_env = os.getenv('MSEC_PASS', '').strip()

    if not users_env or not passwords_env:
        return []

    # 分割用户名和密码
    usernames = [u.strip() for u in users_env.split(',') if u.strip()]
    passwords = [p.strip() for p in passwords_env.split(',') if p.strip()]

    # 检查数量是否匹配
    if len(usernames) != len(passwords):
        print(f"⚠️ 用户名数量({len(usernames)})与密码数量({len(passwords)})不匹配")
        return []

    return list(zip(usernames, passwords))


class MSECApiClient:
    """
    MSEC API客户端 - 负责纯HTTP请求

    这个类专门处理与MSEC平台的HTTP通信，包括：
    - 管理HTTP会话和请求头
    - 发送GET/POST请求
    - 处理请求异常

    Attributes:
        base_url: MSEC平台的基础URL
        timeout: 请求超时时间（秒）
        session: HTTP会话对象
    """

    def __init__(self, base_url: str = MSECConfig.BASE_URL, timeout: int = MSECConfig.TIMEOUT) -> None:
        """
        初始化API客户端

        Args:
            base_url: MSEC平台的基础URL
            timeout: 请求超时时间（秒）
        """
        self.base_url: str = base_url
        self.timeout: int = timeout
        self.session: requests.Session = requests.Session()
        self.session.headers.update(MSECConfig.DEFAULT_HEADERS)

    def set_authorization(self, token: str) -> None:
        """
        设置授权Token

        Args:
            token: JWT授权令牌
        """
        self.session.headers['Authorization'] = token

    def make_request(self, method: str, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Optional[requests.Response]:
        """
        发送HTTP请求

        Args:
            method: HTTP方法（GET/POST）
            endpoint: API端点路径
            data: 请求数据（仅POST请求使用）

        Returns:
            HTTP响应对象，失败时返回None

        Raises:
            不会抛出异常，所有异常都被捕获并返回None
        """
        url = self.base_url + endpoint

        try:
            if method.upper() == 'POST':
                response = self.session.post(url, json=data or {}, timeout=self.timeout)
            else:
                response = self.session.get(url, timeout=self.timeout)

            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
            return None


class CaptchaHandler:
    """
    验证码处理器

    负责验证码的获取和识别，支持：
    - 从MSEC平台获取验证码图片
    - 使用云码API自动识别验证码
    - 处理验证码识别错误

    Attributes:
        api_client: API客户端实例
        captcha_token: 云码API的Token
        enable_auto_captcha: 是否启用自动验证码识别
    """

    def __init__(self, api_client: MSECApiClient, captcha_token: Optional[str] = None,
                 enable_auto_captcha: bool = False) -> None:
        """
        初始化验证码处理器

        Args:
            api_client: API客户端实例
            captcha_token: 云码API的Token
            enable_auto_captcha: 是否启用自动验证码识别
        """
        self.api_client: MSECApiClient = api_client
        self.captcha_token: Optional[str] = captcha_token
        self.enable_auto_captcha: bool = enable_auto_captcha

    def get_captcha(self) -> Tuple[Optional[str], Optional[str]]:
        """获取验证码"""
        print("🔍 正在获取验证码...")
        response = self.api_client.make_request('POST', APIEndpoints.CAPTCHA)

        if not response:
            return None, None

        try:
            data = response.json()
            if data.get('status') != ResponseStatus.SUCCESS:
                print(f"❌ 获取验证码失败: {data}")
                return None, None

            captcha_data = data.get('data', {})
            captcha_id = captcha_data.get('id')
            captcha_image = captcha_data.get('captcha')

            if captcha_id and captcha_image:
                print(f"🔍 验证码图片: {captcha_image}")
                print(f"📋 验证码ID: {captcha_id}")
                return captcha_id, captcha_image

        except json.JSONDecodeError as e:
            print(f"❌ 解析验证码响应失败: {e}")

        return None, None

    def recognize_captcha(self, captcha_image: str) -> Optional[str]:
        """
        使用第三方API识别验证码

        Args:
            captcha_image: base64编码的验证码图片

        Returns:
            识别结果，失败返回None
        """
        if not self.enable_auto_captcha or not self.captcha_token:
            return None

        try:
            print("🤖 正在自动识别验证码...")

            url = MSECConfig.YUNMA_API_URL
            data = {
                "token": self.captcha_token,
                "type": MSECConfig.CAPTCHA_TYPE,  # 验证码类型
                "image": captcha_image,
            }
            headers = {
                "Content-Type": "application/json"
            }

            response = requests.post(url, headers=headers, json=data, timeout=MSECConfig.CAPTCHA_TIMEOUT)
            result = response.json()

            if result.get("code") == CaptchaErrorCodes.SUCCESS:
                captcha_result = result.get("data", {}).get("data", "")
                print(f"🤖 验证码识别成功: {captcha_result}")
                return captcha_result
            else:
                error_msg = CaptchaErrorCodes.ERROR_MESSAGES.get(result.get("code", 0), f"未知错误(代码: {result.get('code', 0)})")
                print(f"❌ 验证码识别失败: {error_msg}")
                return None

        except Exception as e:
            print(f"❌ 验证码识别异常: {e}")
            return None


class AuthManager:
    """
    认证管理器

    负责用户认证和Token管理，包括：
    - 用户登录认证
    - JWT Token的存储和加载
    - 验证码重试机制
    - Token有效性检查

    Attributes:
        api_client: API客户端实例
        captcha_handler: 验证码处理器实例
        username: 用户名
        password: 密码
        jwt_token: JWT认证令牌
        enable_token_storage: 是否启用Token存储
        token_storage: Token存储实例
    """

    def __init__(self, api_client: MSECApiClient, captcha_handler: CaptchaHandler,
                 username: str, password: str, enable_token_storage: bool = True) -> None:
        """
        初始化认证管理器

        Args:
            api_client: API客户端实例
            captcha_handler: 验证码处理器实例
            username: 用户名
            password: 密码
            enable_token_storage: 是否启用Token存储
        """
        self.api_client: MSECApiClient = api_client
        self.captcha_handler: CaptchaHandler = captcha_handler
        self.username: str = username
        self.password: str = password
        self.jwt_token: Optional[str] = None
        self.enable_token_storage: bool = enable_token_storage

        # Token存储
        if self.enable_token_storage:
            self.token_storage: UnifiedTokenStorage = UnifiedTokenStorage(MSECConfig.DEFAULT_TOKEN_FILE)
            # 尝试加载已保存的Token
            self._load_saved_token()

    def _load_saved_token(self) -> bool:
        """加载已保存的Token"""
        if not self.enable_token_storage:
            return False

        try:
            token = self.token_storage.load_token(self.username, self.password)
            if token:
                self.jwt_token = token
                self.api_client.set_authorization(token)
                print("✅ 已加载保存的Token")
                return True
        except Exception as e:
            print(f"⚠️ 加载Token失败: {e}")

        return False

    def _save_token(self, token: str, expire_time: Optional[int] = None) -> bool:
        """保存Token到本地"""
        if not self.enable_token_storage:
            return False

        try:
            return self.token_storage.save_token(
                self.username,
                token,
                self.password,
                expire_time
            )
        except Exception as e:
            print(f"⚠️ 保存Token失败: {e}")
            return False

    def is_logged_in(self) -> bool:
        """检查是否已登录（有有效Token）"""
        if not self.jwt_token:
            return False

        if self.enable_token_storage:
            return self.token_storage.is_token_valid(self.username, self.password)

        return True

    def login(self, captcha_id: str, captcha_answer: str) -> bool:
        """用户登录"""
        print("🔐 正在登录...")

        login_data = {
            "captcha_answer": captcha_answer,
            "captcha_id": captcha_id,
            "password": self.password,
            "username": self.username
        }

        response = self.api_client.make_request('POST', APIEndpoints.LOGIN, login_data)

        if not response:
            return False

        try:
            data = response.json()
            if data.get('status') != ResponseStatus.SUCCESS:
                print(f"❌ 登录失败: {data}")
                return False

            token = data.get('data', {}).get('token')
            if token:
                self.jwt_token = token
                self.api_client.set_authorization(token)

                # 尝试解析Token过期时间并保存
                expire_time = None
                try:
                    import jwt as jwt_lib
                    decoded = jwt_lib.decode(token, options={"verify_signature": False})
                    expire_time = decoded.get('exp')
                except:
                    # 如果解析失败，设置默认过期时间
                    expire_time = int(time.time()) + MSECConfig.DEFAULT_TOKEN_EXPIRE_HOURS * 3600

                # 只有登录成功才保存Token到本地
                if self.enable_token_storage:
                    self._save_token(token, expire_time)

                print("✅ 登录成功!")
                return True
            else:
                print("❌ 未获取到token")
                return False

        except json.JSONDecodeError as e:
            print(f"❌ 解析登录响应失败: {e}")
            return False

    def user_login(self, max_attempts: int = MSECConfig.MAX_LOGIN_ATTEMPTS,
                   max_captcha_refresh: int = MSECConfig.MAX_CAPTCHA_REFRESH) -> bool:
        """
        用户登录（支持验证码刷新和重试）

        Args:
            max_attempts: 最大登录尝试次数
            max_captcha_refresh: 每次登录尝试中验证码刷新的最大次数
        """
        # 首先检查是否已有有效Token
        if self.is_logged_in():
            print("✅ 检测到有效Token，无需重新登录")
            return True

        for attempt in range(max_attempts):
            if attempt > 0:
                print(f"🔄 第{attempt + 1}次登录尝试...")

            # 在每次登录尝试中，支持验证码刷新
            login_success = self._attempt_login_with_captcha_refresh(max_captcha_refresh)

            if login_success:
                return True
            else:
                if attempt < max_attempts - 1:
                    print(f"❌ 登录失败，还有 {max_attempts - attempt - 1} 次机会")
                    print("🔄 将刷新验证码重新尝试...")

        print("❌ 登录失败，已达到最大尝试次数")
        return False

    def _attempt_login_with_captcha_refresh(self, max_refresh: int = 2) -> bool:
        """
        单次登录尝试，支持验证码刷新

        Args:
            max_refresh: 验证码刷新的最大次数

        Returns:
            登录是否成功
        """
        for refresh_count in range(max_refresh + 1):  # +1 因为第一次不算刷新
            if refresh_count > 0:
                print(f"🔄 刷新验证码 (第{refresh_count}次)")

            # 获取验证码
            captcha_id, captcha_image = self.captcha_handler.get_captcha()
            if not captcha_id:
                print("❌ 验证码获取失败")
                if refresh_count < max_refresh:
                    continue  # 尝试刷新
                else:
                    return False

            # 尝试识别验证码（自动或手动）
            captcha_answer = self._get_captcha_answer(captcha_image, refresh_count)
            if not captcha_answer:
                if refresh_count < max_refresh:
                    print("⚠️ 验证码获取失败，尝试刷新...")
                    continue
                else:
                    return False

            # 尝试登录
            if self.login(captcha_id, captcha_answer):
                return True
            else:
                print("❌ 验证码错误")
                if refresh_count < max_refresh:
                    print("🔄 将刷新验证码重新尝试...")
                    continue
                else:
                    return False

        return False

    def _get_captcha_answer(self, captcha_image: Optional[str], refresh_count: int) -> Optional[str]:
        """
        获取验证码答案（仅自动识别，服务器环境不支持手动输入）

        Args:
            captcha_image: 验证码图片base64
            refresh_count: 当前刷新次数

        Returns:
            验证码答案，失败返回None
        """
        # 只尝试自动识别验证码
        if captcha_image:
            captcha_answer = self.captcha_handler.recognize_captcha(captcha_image)

            # 如果自动识别成功，直接返回
            if captcha_answer:
                return captcha_answer

            # 自动识别失败的提示
            if refresh_count == 0:
                print("🤖 自动识别失败，尝试刷新验证码...")
                return None  # 触发刷新
            else:
                print("🤖 多次自动识别失败，无法继续（服务器环境不支持手动输入）")
                return None
        else:
            print("❌ 未启用自动验证码识别或验证码图片为空，无法继续")
            return None


def get_yunma_token() -> Optional[str]:
    """从环境变量获取云码Token"""
    return os.getenv('YUNMA_TOKEN', '').strip() or None


class UserDetail:
    """用户详细信息"""
    def __init__(self, username: str):
        self.username = username
        self.success = False
        self.error_msg = ""
        self.user_info: Optional[Dict[str, Any]] = None
        self.points_info: Optional[Dict[str, Any]] = None
        self.roles_info: Optional[Dict[str, Any]] = None
        self.checkin_history: Optional[Dict[str, Any]] = None
        self.checkin_success = False


class MSECService:
    """
    MSEC业务服务类 - 负责业务逻辑协调

    这是MSEC签到功能的主要服务类，协调各个组件完成：
    - 用户认证和登录
    - 获取用户信息、积分、角色等数据
    - 获取签到历史记录
    - 执行签到操作
    - 完整的签到流程管理

    Attributes:
        username: 用户名
        password: 密码
        max_captcha_refresh: 验证码最大刷新次数
        api_client: API客户端实例
        captcha_handler: 验证码处理器实例
        auth_manager: 认证管理器实例
    """

    def __init__(self, username: str, password: str, enable_token_storage: bool = True,
                 captcha_token: Optional[str] = None, enable_auto_captcha: bool = False,
                 max_captcha_refresh: int = MSECConfig.MAX_CAPTCHA_REFRESH) -> None:
        """
        初始化MSEC服务

        Args:
            username: 用户名
            password: 密码
            enable_token_storage: 是否启用Token存储
            captcha_token: 云码API的Token
            enable_auto_captcha: 是否启用自动验证码识别
            max_captcha_refresh: 验证码最大刷新次数
        """
        self.username: str = username
        self.password: str = password
        self.max_captcha_refresh: int = max_captcha_refresh

        # 初始化组件
        self.api_client: MSECApiClient = MSECApiClient()
        self.captcha_handler: CaptchaHandler = CaptchaHandler(self.api_client, captcha_token, enable_auto_captcha)
        self.auth_manager: AuthManager = AuthManager(self.api_client, self.captcha_handler, username, password, enable_token_storage)

        # 验证云码Token配置（MSEC始终需要验证码）
        if not captcha_token:
            print("⚠️ 警告: 未配置云码Token，MSEC平台始终需要验证码识别")
        if not enable_auto_captcha:
            print("⚠️ 警告: 未启用自动验证码识别，登录可能失败")

    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """获取用户信息"""
        if not self.auth_manager.jwt_token:
            print("❌ 未登录，无法获取用户信息")
            return None

        print("👤 正在获取用户信息...")
        self.api_client.session.headers['Referer'] = 'https://msec.nsfocus.com/'

        response = self.api_client.make_request('POST', APIEndpoints.USER_INFO)

        if not response:
            return None

        try:
            data = response.json()
            if data.get('status') != ResponseStatus.SUCCESS:
                print(f"❌ 获取用户信息失败: {data}")
                return None

            user_info = data.get('data', {}).get('user', {})
            if user_info:
                print(f"👤 用户: {user_info.get('username')} (ID: {user_info.get('id')})")
                return user_info

        except json.JSONDecodeError as e:
            print(f"❌ 解析用户信息响应失败: {e}")

        return None

    def get_points(self) -> Optional[Dict[str, Any]]:
        """获取积分信息"""
        if not self.auth_manager.jwt_token:
            print("❌ 未登录，无法获取积分信息")
            return None

        print("💰 正在获取积分信息...")
        response = self.api_client.make_request('POST', APIEndpoints.POINTS)

        if not response:
            return None

        try:
            data = response.json()
            if data.get('status') != ResponseStatus.SUCCESS:
                print(f"❌ 获取积分信息失败: {data}")
                return None

            points_data = data.get('data', {})
            if 'accrued' in points_data and 'total' in points_data:
                print(f"💰 积分: {points_data.get('accrued')}/{points_data.get('total')}")
                return points_data

        except json.JSONDecodeError as e:
            print(f"❌ 解析积分信息响应失败: {e}")

        return None

    def get_roles(self) -> Optional[Dict[str, Any]]:
        """获取用户角色信息"""
        if not self.auth_manager.jwt_token:
            print("❌ 未登录，无法获取角色信息")
            return None

        print("🎭 正在获取角色信息...")
        role_data = {"limit": 100, "offset": 0}

        response = self.api_client.make_request('POST', APIEndpoints.ROLES, role_data)

        if not response:
            return None

        try:
            data = response.json()
            if data.get('status') != ResponseStatus.SUCCESS:
                print(f"❌ 获取角色信息失败: {data}")
                return None

            roles_data = data.get('data', {})
            role_list = roles_data.get('list', [])

            if role_list:
                role_names: list[str] = []
                for role in role_list:
                    role_name = role.get('role_name', '未知角色')
                    # 处理Unicode编码
                    if isinstance(role_name, str) and '\\u' in role_name:
                        try:
                            role_name = role_name.encode().decode('unicode_escape')
                        except:
                            pass

                    expire_time = role.get('expire_time', 0)
                    if expire_time == 0:
                        role_names.append(f"{role_name}(永久)")
                    else:
                        # 将时间戳转换为年月日格式
                        try:
                            from datetime import datetime
                            expire_date = datetime.fromtimestamp(expire_time).strftime('%Y-%m-%d')
                            role_names.append(f"{role_name}(至{expire_date})")
                        except:
                            role_names.append(f"{role_name}(限时)")

                print(f"🎭 角色: {', '.join(role_names)}")
            else:
                print("🎭 角色: 无")

            return roles_data

        except json.JSONDecodeError as e:
            print(f"❌ 解析角色信息响应失败: {e}")

        return None

    def _format_checkin_time(self, checkin_time: Any) -> str:
        """
        格式化签到时间显示

        Args:
            checkin_time: 签到时间（时间戳或字符串）

        Returns:
            格式化后的时间字符串
        """
        try:
            from datetime import datetime
            if isinstance(checkin_time, (int, float)):
                # 检查是否为毫秒级时间戳（13位数字）
                if checkin_time > 9999999999:  # 大于10位数字，可能是毫秒
                    timestamp = checkin_time / 1000  # 转换为秒
                else:
                    timestamp = checkin_time

                # 转换为datetime对象
                dt = datetime.fromtimestamp(timestamp)

                # 获取星期几（中文）
                weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
                weekday_cn = weekdays[dt.weekday()]

                # 格式化时间显示 - 完整时间戳格式
                checkin_date = dt.strftime('%Y-%m-%d %H:%M:%S')
                return f"{checkin_date} ({weekday_cn})"
            else:
                # 如果已经是字符串格式
                return str(checkin_time)
        except Exception:
            return f"{checkin_time} (时间格式错误)"

    def get_checkin_history(self, start_date: str, days: int = 5) -> Optional[Dict[str, Any]]:
        """获取签到历史"""
        if not self.auth_manager.jwt_token:
            print("❌ 未登录，无法获取签到历史")
            return None

        print(f"📅 正在获取签到历史 (从 {start_date} 开始，{days} 天)...")
        history_data: Dict[str, Any] = {"start_date": start_date, "days": days}

        response = self.api_client.make_request('POST', APIEndpoints.CHECKIN_HISTORY, history_data)

        if not response:
            return None

        try:
            data = response.json()
            if data.get('status') != ResponseStatus.SUCCESS:
                print(f"❌ 获取签到历史失败: {data}")
                return None

            history_data = data.get('data', {})
            records_list = history_data.get('records_list', {})
            total_records = records_list.get('total', 0)
            checkin_list = records_list.get('list', [])

            print(f"📅 本周签到记录: {total_records}次")

            # 显示具体的签到时间 - 完全遵循控制台日志格式
            if checkin_list:
                print("📅 签到时间明细:")
                for record in checkin_list:
                    checkin_time = record.get('checkin_time', '')
                    if checkin_time:
                        formatted_time = self._format_checkin_time(checkin_time)
                        print(f"   • {formatted_time}")

            return history_data

        except json.JSONDecodeError as e:
            print(f"❌ 解析签到历史响应失败: {e}")

        return None

    def checkin(self) -> bool:
        """执行签到"""
        if not self.auth_manager.jwt_token:
            print("❌ 未登录，无法签到")
            return False

        print("📝 正在执行签到...")
        response = self.api_client.make_request('POST', APIEndpoints.CHECKIN)

        if not response:
            return False

        try:
            data = response.json()
            status = data.get('status')

            if status == ResponseStatus.SUCCESS:
                print("✅ 签到成功！")
                return True
            elif status == ResponseStatus.ALREADY_CHECKED:
                print("⚠️ 今日已签到")
                return True
            else:
                print(f"❌ 签到失败 (状态: {status})")
                return False

        except json.JSONDecodeError as e:
            print(f"❌ 解析签到响应失败: {e}")
            return False

    def run_full_process(self) -> Tuple[bool, UserDetail]:
        """运行完整流程，返回成功状态和用户详细信息"""
        print("🚀 开始MSEC自动签到流程")

        # 创建用户详细信息对象
        user_detail = UserDetail(self.username)

        # 1. 登录（使用配置的验证码刷新次数）
        if not self.auth_manager.user_login(max_captcha_refresh=self.max_captcha_refresh):
            user_detail.error_msg = "登录失败"
            return False, user_detail

        # 2. 获取用户信息
        user_detail.user_info = self.get_user_info()

        # 3. 获取积分信息
        user_detail.points_info = self.get_points()

        # 4. 获取角色信息
        user_detail.roles_info = self.get_roles()

        # 5. 获取签到历史
        start_date, days = get_week_checkin_params()
        user_detail.checkin_history = self.get_checkin_history(start_date, days)

        # 6. 执行签到
        success = self.checkin()
        user_detail.checkin_success = success

        if success:
            print("🎉 签到流程完成！")
        else:
            print("❌ 签到流程失败")
            user_detail.error_msg = "签到失败"

        return success, user_detail


class MultiUserResult:
    """多用户执行结果"""

    def __init__(self):
        self.total_users = 0
        self.success_users: List[str] = []
        self.failed_users: List[Tuple[str, str]] = []  # (username, error)
        self.user_details: List[UserDetail] = []  # 用户详细信息
        self.start_time = datetime.now()
        self.end_time: Optional[datetime] = None

    def add_success(self, username: str, user_detail: Optional[UserDetail] = None):
        """添加成功用户"""
        self.success_users.append(username)
        if user_detail:
            user_detail.success = True
            self.user_details.append(user_detail)

    def add_failure(self, username: str, error: str, user_detail: Optional[UserDetail] = None):
        """添加失败用户"""
        self.failed_users.append((username, error))
        if user_detail:
            user_detail.success = False
            user_detail.error_msg = error
            self.user_details.append(user_detail)

    def finish(self):
        """完成执行"""
        self.end_time = datetime.now()

    def get_summary(self) -> str:
        """获取执行摘要"""
        duration = (self.end_time - self.start_time).total_seconds() if self.end_time else 0

        summary = [
            "=" * 50,
            "📊 签到执行报告",
            "=" * 50,
            f"⏱️ 执行时间: {duration:.1f}秒",
            f"👥 总用户数: {self.total_users}",
            f"✅ 成功用户: {len(self.success_users)}",
            f"❌ 失败用户: {len(self.failed_users)}",
            ""
        ]

        if self.success_users:
            summary.append("✅ 成功用户列表:")
            for user in self.success_users:
                summary.append(f"   • {user}")
            summary.append("")

        if self.failed_users:
            summary.append("❌ 失败用户列表:")
            for user, error in self.failed_users:
                summary.append(f"   • {user}: {error}")
            summary.append("")

        summary.append("=" * 50)
        return "\n".join(summary)


def run_user_checkin(users: List[Tuple[str, str]], yunma_token: Optional[str] = None,
                          max_captcha_refresh: int = 2) -> MultiUserResult:
    """
    运行多用户签到

    Args:
        users: 用户名密码对列表
        yunma_token: 云码验证码识别Token
        max_captcha_refresh: 验证码刷新次数

    Returns:
        执行结果
    """
    result = MultiUserResult()
    result.total_users = len(users)

    # 检查云码Token（MSEC始终需要验证码）
    if not yunma_token:
        print("❌ 错误: 云码Token未配置，无法进行验证码识别")
        print("❌ MSEC平台始终需要验证码，必须配置云码Token")
        # 将所有用户标记为失败
        for username, _ in users:
            result.add_failure(username, "云码Token未配置")
        result.finish()
        return result

    print("=" * 50)
    print("🚀 开始MSEC自动签到")

    for i, (username, password) in enumerate(users, 1):
        print(f"\n📋 处理用户 {i}/{len(users)}: {username}")
        print("-" * 30)

        try:
            # 创建服务
            service = MSECService(
                username=username,
                password=password,
                enable_token_storage=True,
                captcha_token=yunma_token,
                enable_auto_captcha=bool(yunma_token),
                max_captcha_refresh=max_captcha_refresh
            )

            # 执行签到流程
            success, user_detail = service.run_full_process()

            if success:
                result.add_success(username, user_detail)
                print(f"✅ 用户 {username} 签到成功")
            else:
                result.add_failure(username, user_detail.error_msg or "签到流程失败", user_detail)
                print(f"❌ 用户 {username} 签到失败")

        except Exception as e:
            error_msg = str(e)
            user_detail = UserDetail(username)
            user_detail.error_msg = error_msg
            result.add_failure(username, error_msg, user_detail)
            print(f"❌ 用户 {username} 处理异常: {error_msg}")

        # 用户间间隔，避免请求过于频繁
        if i < len(users):
            print(f"⏱️ 等待{MSECConfig.USER_INTERVAL_SECONDS}秒后处理下一个用户...")
            time.sleep(MSECConfig.USER_INTERVAL_SECONDS)

    result.finish()
    return result



def main():
    """主函数 - 从环境变量读取账号密码进行批量签到"""
    print("🎯 NSFOCUS签到")
    print("=" * 50)

    # 从环境变量读取用户配置
    env_users = parse_env_users()
    yunma_token = get_yunma_token()

    if not env_users:
        print("❌ 未检测到环境变量配置")
        print("\n📋 请设置以下环境变量:")
        print("   MSEC_USER=user1,user2,user3")
        print("   MSEC_PASS=pass1,pass2,pass3")
        print("   YUNMA_TOKEN=your_yunma_token")
        print("\n💡 示例:")
        print("   export MSEC_USER='alice,bob'")
        print("   export MSEC_PASS='pwd1,pwd2'")
        print("   export YUNMA_TOKEN='token123'")
        return False

    print(f"🔍 检测到环境变量配置: {len(env_users)} 个用户")
    print("📋 用户列表:")
    for i, (username, _) in enumerate(env_users, 1):
        print(f"   {i}. {username}")

    print(f"\n🤖 云码Token: {'已配置' if yunma_token else '未配置'}")

    # 检查云码Token配置（MSEC始终需要验证码，必须配置）
    if not yunma_token:
        print("❌ 错误: 未配置云码Token")
        print("❌ MSEC平台始终需要验证码，必须配置自动验证码识别")
        print("\n📋 请设置云码Token环境变量:")
        print("   YUNMA_TOKEN=your_yunma_token")
        print("\n💡 获取云码Token:")
        print("   1. 访问 http://api.jfbym.com/ 注册账号")
        print("   2. 充值并获取API Token")
        print("   3. 设置环境变量: export YUNMA_TOKEN='your_token'")
        return False

    # 配置验证码刷新次数
    max_refresh = MSECConfig.MAX_CAPTCHA_REFRESH  # 默认刷新次数
    print(f"🔄 验证码刷新次数: {max_refresh}")

    # 执行多用户签到
    result = run_user_checkin(env_users, yunma_token, max_refresh)

    # 显示执行报告
    print(result.get_summary())

    # 返回执行结果
    return len(result.failed_users) == 0


if __name__ == "__main__":
    main()